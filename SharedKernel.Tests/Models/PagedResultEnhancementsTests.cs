using SharedKernel.Models;
using Xunit;

namespace SharedKernel.Tests.Models;

/// <summary>
/// Testy pro nové vyle<PERSON>š<PERSON>í PagedResult třídy.
/// </summary>
public class PagedResultEnhancementsTests
{
    [Fact]
    public void StartIndex_ShouldCalculateCorrectly()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1", "item2" }, 10, 2, 5);

        // Assert
        Assert.Equal(6, result.StartIndex); // (2-1) * 5 + 1 = 6
    }

    [Fact]
    public void EndIndex_ShouldCalculateCorrectly_WhenPageNotFull()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1", "item2" }, 7, 2, 5);

        // Assert
        Assert.Equal(7, result.EndIndex); // Min(6 + 5 - 1, 7) = 7
    }

    [Fact]
    public void EndIndex_ShouldCalculateCorrectly_WhenPageFull()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1", "item2", "item3", "item4", "item5" }, 15, 2, 5);

        // Assert
        Assert.Equal(10, result.EndIndex); // Min(6 + 5 - 1, 15) = 10
    }

    [Fact]
    public void IsEmpty_ShouldReturnTrue_WhenNoItems()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string>(), 0, 1, 10);

        // Assert
        Assert.True(result.IsEmpty);
    }

    [Fact]
    public void IsEmpty_ShouldReturnFalse_WhenHasItems()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1" }, 1, 1, 10);

        // Assert
        Assert.False(result.IsEmpty);
    }

    [Fact]
    public void IsFirstPage_ShouldReturnTrue_WhenPageNumberIsOne()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1" }, 10, 1, 5);

        // Assert
        Assert.True(result.IsFirstPage);
    }

    [Fact]
    public void IsFirstPage_ShouldReturnFalse_WhenPageNumberIsNotOne()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1" }, 10, 2, 5);

        // Assert
        Assert.False(result.IsFirstPage);
    }

    [Fact]
    public void IsLastPage_ShouldReturnTrue_WhenOnLastPage()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1" }, 6, 2, 5); // 2 pages total

        // Assert
        Assert.True(result.IsLastPage);
    }

    [Fact]
    public void IsLastPage_ShouldReturnFalse_WhenNotOnLastPage()
    {
        // Arrange & Act
        var result = PagedResult<string>.Ok(new List<string> { "item1" }, 10, 1, 5); // 2 pages total

        // Assert
        Assert.False(result.IsLastPage);
    }

    [Fact]
    public void Empty_ShouldCreateEmptyResult()
    {
        // Arrange & Act
        var result = PagedResult<string>.Empty(20);

        // Assert
        Assert.True(result.Succeeded);
        Assert.Empty(result.Items);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(1, result.PageNumber);
        Assert.Equal(20, result.PageSize);
        Assert.True(result.IsEmpty);
        Assert.True(result.IsFirstPage);
        // Pro prázdný výsledek není IsLastPage true, protože TotalPages = 0, ale PageNumber = 1
        Assert.False(result.IsLastPage);
    }

    [Fact]
    public void ValidatePaging_ShouldThrowException_WhenPageSizeExceeds1000()
    {
        // Arrange & Act & Assert
        var exception = Assert.Throws<ArgumentOutOfRangeException>(() =>
            PagedResult<string>.Ok(new List<string>(), 0, 1, 1001));

        Assert.Contains("PageSize cannot exceed 1000 items", exception.Message);
    }

    [Fact]
    public void CreateSafe_ShouldReturnError_WhenSourceIsNull()
    {
        // Arrange & Act
        var result = PagedResult<string>.CreateSafe<int>(
            null!, 1, 10, (Func<int, string>)(x => x.ToString()));

        // Assert
        Assert.False(result.Succeeded);
        Assert.Contains("Source query cannot be null", result.Errors);
    }

    [Fact]
    public void CreateSafe_ShouldReturnError_WhenSelectorIsNull()
    {
        // Arrange
        var source = new List<int> { 1, 2, 3 }.AsQueryable();

        // Act
        var result = PagedResult<string>.CreateSafe(
            source, 1, 10, (System.Linq.Expressions.Expression<Func<int, string>>)null!);

        // Assert
        Assert.False(result.Succeeded);
        Assert.Contains("Selector cannot be null", result.Errors);
    }

    [Fact]
    public void CreateSafe_ShouldApplyOrderBy_WhenProvided()
    {
        // Arrange
        var source = new List<int> { 3, 1, 2 }.AsQueryable();

        // Act
        var result = PagedResult<string>.CreateSafe(
            source, 1, 10,
            (Func<int, string>)(x => x.ToString()),
            orderBy: q => q.OrderBy(x => x));

        // Assert
        Assert.True(result.Succeeded);
        Assert.Equal(3, result.Items.Count);
        Assert.Equal("1", result.Items[0]);
        Assert.Equal("2", result.Items[1]);
        Assert.Equal("3", result.Items[2]);
    }

    [Fact]
    public void CreateSafe_ShouldReturnEmptyResult_WhenSourceIsEmpty()
    {
        // Arrange
        var source = new List<int>().AsQueryable();

        // Act
        var result = PagedResult<string>.CreateSafe(
            source, 1, 10, (Func<int, string>)(x => x.ToString()));

        // Assert
        Assert.True(result.Succeeded);
        Assert.Empty(result.Items);
        Assert.Equal(0, result.TotalCount);
        Assert.True(result.IsEmpty);
    }
}
