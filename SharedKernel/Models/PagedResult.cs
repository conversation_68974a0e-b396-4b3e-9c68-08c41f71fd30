using System.Linq.Expressions;

namespace SharedKernel.Models;

/// <summary>
/// Stránkovaný výsledek operace, kter<PERSON> rozšiřuje Result pattern o metadata pro stránkování.
/// Kombinuje funkcionalitu Result<List<T>> s informacemi o stránkování.
/// </summary>
/// <typeparam name="T">Typ položek v kolekci</typeparam>
public record PagedResult<T> : Result<List<T>>
{
    /// <summary>
    /// Položky na aktuální stránce.
    /// </summary>
    public List<T> Items => Data ?? new List<T>();

    /// <summary>
    /// Číslo aktuální stránky (začíná od 1).
    /// </summary>
    public int PageNumber { get; }

    /// <summary>
    /// Celkový počet stránek.
    /// </summary>
    public int TotalPages { get; }

    /// <summary>
    /// Celkový počet položek napříč v<PERSON><PERSON>i stránkami.
    /// </summary>
    public int TotalCount { get; }

    /// <summary>
    /// Velikost stránky (počet položek na stránku).
    /// </summary>
    public int PageSize { get; }

    /// <summary>
    /// Indikuje, zda existuje předchozí stránka.
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Indikuje, zda existuje následující stránka.
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Index prvního prvku na aktuální stránce (začíná od 1).
    /// </summary>
    public int StartIndex => (PageNumber - 1) * PageSize + 1;

    /// <summary>
    /// Index posledního prvku na aktuální stránce.
    /// </summary>
    public int EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount);

    /// <summary>
    /// Indikuje, zda je stránka prázdná (neobsahuje žádné položky).
    /// </summary>
    public bool IsEmpty => !Items.Any();

    /// <summary>
    /// Indikuje, zda je aktuální stránka první stránkou.
    /// </summary>
    public bool IsFirstPage => PageNumber == 1;

    /// <summary>
    /// Indikuje, zda je aktuální stránka poslední stránkou.
    /// </summary>
    public bool IsLastPage => PageNumber == TotalPages;

    private PagedResult(bool succeeded, List<T> items, int count, int pageNumber, int pageSize, string[] errors)
        : base(succeeded, items, errors)
    {
        TotalCount = count;
        PageSize = pageSize;
        PageNumber = pageNumber;
        TotalPages = pageSize > 0 ? (int)Math.Ceiling(count / (double)pageSize) : 0;
    }

    #region Static Factory Methods

    /// <summary>
    /// Vytvoří úspěšný stránkovaný výsledek.
    /// </summary>
    /// <param name="items">Položky na stránce</param>
    /// <param name="totalCount">Celkový počet položek</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <returns>Úspěšný PagedResult</returns>
    public static PagedResult<T> Ok(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        ValidatePaging(pageNumber, pageSize);
        return new PagedResult<T>(true, items, totalCount, pageNumber, pageSize, Array.Empty<string>());
    }

    /// <summary>
    /// Vytvoří neúspěšný stránkovaný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Neúspěšný PagedResult</returns>
    public new static PagedResult<T> Error(params string[] errors)
        => new PagedResult<T>(false, new List<T>(), 0, 1, 10, errors);

    /// <summary>
    /// Asynchronně vytvoří úspěšný stránkovaný výsledek.
    /// </summary>
    /// <param name="items">Položky na stránce</param>
    /// <param name="totalCount">Celkový počet položek</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <returns>Task s úspěšným PagedResult</returns>
    public static Task<PagedResult<T>> OkAsync(List<T> items, int totalCount, int pageNumber, int pageSize)
        => Task.FromResult(Ok(items, totalCount, pageNumber, pageSize));

    /// <summary>
    /// Asynchronně vytvoří neúspěšný stránkovaný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Task s neúspěšným PagedResult</returns>
    public new static Task<PagedResult<T>> ErrorAsync(params string[] errors)
        => Task.FromResult(Error(errors));

    /// <summary>
    /// Vytvoří prázdný stránkovaný výsledek.
    /// </summary>
    /// <param name="pageSize">Velikost stránky (výchozí 10)</param>
    /// <returns>Prázdný PagedResult</returns>
    public static PagedResult<T> Empty(int pageSize = 10)
        => Ok(new List<T>(), 0, 1, pageSize);

    #endregion

    #region CreateSafe Methods

    /// <summary>
    /// Vytvoří PagedResult s validací a lepším error handling.
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v paměti.
    /// </summary>
    /// <param name="source">Zdrojový IQueryable</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="map">Funkce pro mapování</param>
    /// <param name="orderBy">Volitelná funkce pro řazení</param>
    /// <returns>PagedResult s daty nebo chybami</returns>
    public static PagedResult<T> CreateSafe<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Func<TSource, T> map,
        Func<IQueryable<TSource>, IQueryable<TSource>>? orderBy = null)
    {
        try
        {
            ValidatePaging(pageNumber, pageSize);

            if (source == null)
                return Error("Source query cannot be null");

            // Aplikace řazení pokud je poskytnuté
            var orderedSource = orderBy?.Invoke(source) ?? source;

            var totalCount = orderedSource.Count();

            if (totalCount == 0)
                return Ok(new List<T>(), 0, pageNumber, pageSize);

            var items = orderedSource
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList()
                .Select(map)
                .ToList();

            return Ok(items, totalCount, pageNumber, pageSize);
        }
        catch (ArgumentException ex)
        {
            return Error($"Invalid paging parameters: {ex.Message}");
        }
        catch (Exception ex)
        {
            return Error($"Error creating paged result: {ex.Message}");
        }
    }

    /// <summary>
    /// Vytvoří PagedResult s Expression projekcí.
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v SQL.
    /// </summary>
    /// <param name="source">Zdrojový IQueryable</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="selector">Expression pro projekci</param>
    /// <param name="orderBy">Volitelná funkce pro řazení</param>
    /// <returns>PagedResult s daty nebo chybami</returns>
    public static PagedResult<T> CreateSafe<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Expression<Func<TSource, T>> selector,
        Func<IQueryable<TSource>, IQueryable<TSource>>? orderBy = null)
    {
        try
        {
            ValidatePaging(pageNumber, pageSize);

            if (source == null)
                return Error("Source query cannot be null");

            if (selector == null)
                return Error("Selector cannot be null");

            // Aplikace řazení pokud je poskytnuté
            var orderedSource = orderBy?.Invoke(source) ?? source;

            var totalCount = orderedSource.Count();

            if (totalCount == 0)
                return Ok(new List<T>(), 0, pageNumber, pageSize);

            var items = orderedSource
                .Select(selector)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return Ok(items, totalCount, pageNumber, pageSize);
        }
        catch (ArgumentException ex)
        {
            return Error($"Invalid paging parameters: {ex.Message}");
        }
        catch (Exception ex)
        {
            return Error($"Error creating paged result: {ex.Message}");
        }
    }

    #endregion

    #region Legacy Create Methods (Obsolete)

    /// <summary>
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v paměti.
    /// </summary>
    /// <remarks>Tato metoda je zastaralá. Použijte CreateSafe pro lepší error handling.</remarks>
    [Obsolete("Use CreateSafe method instead for better error handling and validation.")]
    public static PagedResult<T> Create<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Func<TSource, T> map)
    {
        return CreateSafe(source, pageNumber, pageSize, map);
    }

    /// <summary>
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v SQL.
    /// </summary>
    /// <remarks>Tato metoda je zastaralá. Použijte CreateSafe pro lepší error handling.</remarks>
    [Obsolete("Use CreateSafe method instead for better error handling and validation.")]
    public static PagedResult<T> Create<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Expression<Func<TSource, T>> selector)
    {
        return CreateSafe(source, pageNumber, pageSize, selector);
    }

    #endregion

    #region Helpers

    private static void ValidatePaging(int pageNumber, int pageSize)
    {
        if (pageNumber < 1)
            throw new ArgumentOutOfRangeException(nameof(pageNumber), "PageNumber must be >= 1.");
        if (pageSize < 1)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "PageSize must be > 0.");
        if (pageSize > 1000)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "PageSize cannot exceed 1000 items.");
    }

    #endregion
}
